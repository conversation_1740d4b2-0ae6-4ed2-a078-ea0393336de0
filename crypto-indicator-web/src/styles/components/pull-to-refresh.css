/**
 * Pull-to-Refresh Component Styles
 *
 * Styles for the PullToRefresh component including container,
 * refresh indicator, spinner, and animations.
 *
 * Dependencies: variables.css, base.css
 */

.pull-to-refresh-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  will-change: transform;
}

.pull-to-refresh-content {
  position: relative;
  width: 100%;
  min-height: 100%;
}

/* Refresh Indicator */
.refresh-indicator {
  position: fixed;
  top: 20px;
  left: 50%;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  justify-content: center;
  min-width: 200px;
  padding: 16px 20px;
  text-align: center;
  background: rgb(var(--bg-primary-rgb), 0.95);
  border: 1px solid var(--border-primary);
  border-radius: 16px;
  box-shadow: var(--glass-shadow);
  backdrop-filter: var(--glass-blur);
  transition: opacity 0.2s ease;
  will-change: transform, opacity;
}

.refresh-indicator.ready {
  background: rgb(var(--accent-teal-rgb), 0.1);
  border-color: var(--accent-teal);
  box-shadow: 0 4px 20px rgb(148 226 213 / 0.3);
}

.refresh-indicator.refreshing {
  background: rgb(var(--accent-blue-rgb), 0.1);
  border-color: var(--accent-blue);
  box-shadow: 0 4px 20px rgb(137 180 250 / 0.3);
}

/* Refresh Spinner */
.refresh-spinner {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.spinner-ring {
  position: absolute;
  width: 32px;
  height: 32px;
  border: 2px solid transparent;
  border-top: 2px solid var(--text-secondary);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.refresh-indicator.ready .spinner-ring {
  border-top-color: var(--accent-teal);
  opacity: 0.6;
}

.refresh-indicator.refreshing .spinner-ring {
  border-top-color: var(--accent-blue);
  opacity: 1;
  animation: spin 1s linear infinite;
}

.refresh-icon {
  font-size: 16px;
  color: var(--text-secondary);
  transform-origin: center;
  transition: all 0.2s ease;
}

.refresh-indicator.ready .refresh-icon {
  color: var(--accent-teal);
  transform: rotate(180deg);
}

.refresh-indicator.refreshing .refresh-icon {
  color: var(--accent-blue);
  opacity: 0;
}

/* Refresh Text */
.refresh-text {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
  text-align: center;
  white-space: nowrap;
  transition: color 0.2s ease;
}

.refresh-indicator.ready .refresh-text {
  color: var(--accent-teal);
}

.refresh-indicator.refreshing .refresh-text {
  color: var(--accent-blue);
}

/* Animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* Mobile Optimizations */
@media (width <= 480px) {
  .refresh-indicator {
    top: 10px;
    padding: 12px 16px;
    border-radius: 12px;
  }

  .refresh-spinner {
    width: 28px;
    height: 28px;
  }

  .spinner-ring {
    width: 28px;
    height: 28px;
  }

  .refresh-icon {
    font-size: 14px;
  }

  .refresh-text {
    font-size: 11px;
  }
}

/* Tablet Optimizations */
@media (width <= 768px) {
  .refresh-indicator {
    top: 15px;
    padding: 14px 18px;
    border-radius: 14px;
  }

  .refresh-spinner {
    width: 30px;
    height: 30px;
  }

  .spinner-ring {
    width: 30px;
    height: 30px;
  }

  .refresh-icon {
    font-size: 15px;
  }

  .refresh-text {
    font-size: 11.5px;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .pull-to-refresh-container,
  .refresh-indicator,
  .spinner-ring,
  .refresh-icon,
  .refresh-text {
    transition: none;
    animation: none;
  }

  .refresh-indicator.refreshing .spinner-ring {
    opacity: 0.8;
    animation: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .refresh-indicator {
    background: var(--bg-primary);
    border: 2px solid var(--text-primary);
  }

  .refresh-indicator.ready {
    border-color: var(--accent-teal);
  }

  .refresh-indicator.refreshing {
    border-color: var(--accent-blue);
  }
}
