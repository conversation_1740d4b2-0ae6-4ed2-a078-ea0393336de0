/**
 * Chart Modal Component Styles
 *
 * Styles for the ChartModal component including modal overlay, content,
 * chart container, legend, and close button.
 *
 * Dependencies: variables.css, base.css
 */

/* Chart Modal Styles */
.chart-modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;

  /* iOS Safe Area Support */
  width: 100%;
  width: 100vw;
  height: 100%;
  height: 100vh;
  background: rgb(0 0 0 / 0.95);
  backdrop-filter: var(--glass-blur);
  animation: modal-fade-in 0.3s ease-out;
}

.chart-modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 95vw;
  max-width: 95vw;
  height: 75vh;
  max-height: 75vh;

  /* Ensure modal never exceeds viewport */
  max-height: calc(100vh - 120px);
  padding: 24px;

  /* Add top margin to avoid browser UI overlap */
  margin-top: 60px;
  color: var(--text-primary);
  background: var(--glass-bg-strong);
  border: 1px solid var(--glass-border);
  border-radius: 24px;
  box-shadow: var(--glass-shadow);
  backdrop-filter: var(--glass-blur-strong);
  animation: modal-slide-in 0.3s ease-out;
}

.chart-modal-content::before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 1px;
  content: '';
  background: linear-gradient(90deg, transparent, var(--glass-border), transparent);
}

.chart-header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 15px;
  margin-bottom: 20px;
  border-bottom: 1px solid var(--glass-border);
}

.chart-header::after {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  height: 1px;
  content: '';
  background: linear-gradient(90deg, transparent, var(--accent-cyan), transparent);
}

.chart-header h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  text-shadow: 0 2px 10px rgb(0 0 0 / 0.3);
}

.close-button {
  position: relative;

  /* Ensure close button is always accessible */
  z-index: 1002;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;

  /* Better touch target for mobile */
  min-width: 44px;
  height: 44px;
  min-height: 44px;
  padding: 0;
  font-size: 24px;
  color: var(--text-primary);
  cursor: pointer;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 50%;
  backdrop-filter: var(--glass-blur);
  transition: all 0.3s ease;
}

.close-button:hover {
  background: rgb(255 255 255 / 0.2);
  border-color: rgb(255 255 255 / 0.3);
  box-shadow: 0 4px 16px rgb(0 0 0 / 0.2);
  transform: scale(1.1);
}

.close-button:focus {
  outline: 2px solid var(--accent-cyan);
  outline-offset: 2px;
}

.close-button:active {
  transform: scale(0.95);
}

.chart-container {
  position: relative;
  flex: 1;
  width: 100%;
  min-width: 600px;
  min-height: 400px;
  margin: 20px 0;
  overflow: hidden;
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 16px;
  box-shadow: var(--glass-shadow);
}

/* Mobile Safari specific fixes */
.mobile-safari-fix {
  /* Override styles for mobile Safari with better balance */
  height: auto !important;
  max-height: none !important;
  padding: 16px !important;
  margin: 80px 12px 60px !important;
}

.mobile-safari-fix .chart-container {
  /* Reasonable minimum requirements on mobile Safari */
  min-width: 320px;
  min-height: 350px;
  margin: 12px 0;
}

.chart-container::before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 1px;
  content: '';
  background: linear-gradient(90deg, transparent, var(--accent-teal), transparent);
  opacity: 0.3;
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  justify-content: center;
  padding: 16px;
  margin-top: 20px;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  backdrop-filter: var(--glass-blur);
}

.legend-item {
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 13px;
  font-weight: 500;
  color: var(--text-secondary);
}

.legend-color {
  width: 14px;
  height: 14px;
  border-radius: 3px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 0.3);
}

/* Mobile-specific fixes for browser UI overlap */
@media (width <= 768px) {
  .chart-modal-content {
    /* Better balance: visible bottom but reasonable size */
    height: calc(100vh - 180px);
    max-height: calc(100vh - 180px);

    /* Standard padding */
    padding: 20px;
    padding-top: max(16px, env(safe-area-inset-top, 16px));
    padding-bottom: max(16px, env(safe-area-inset-bottom, 16px));

    /* Moderate margins to clear browser UI */
    margin-top: 80px;
    margin-top: max(80px, env(safe-area-inset-top, 80px));

    margin-bottom: 60px;
    margin-bottom: max(60px, env(safe-area-inset-bottom, 60px));
  }

  .close-button {
    /* Ensure it's always visible */
    z-index: 1003;

    /* Larger close button on mobile */
    width: 48px;
    min-width: 48px;
    height: 48px;
    min-height: 48px;
    font-size: 28px;
  }

  .chart-container {
    /* Reduce minimum width on mobile */
    min-width: 320px;
    min-height: 300px;
    margin: 12px 0;
  }
}

/* iPhone 13 Pro Max and similar large phones */
@media (width <= 428px) and (height <= 926px) {
  .chart-modal-content {
    /* Better balance for iPhone 13 Pro Max */
    height: calc(100vh - 200px);
    max-height: calc(100vh - 200px);

    /* Reasonable padding */
    padding: 16px;

    /* Balanced margins to ensure visibility */
    margin-top: 90px;
    margin-top: max(90px, env(safe-area-inset-top, 90px));
    margin-bottom: 70px;
    margin-bottom: max(70px, env(safe-area-inset-bottom, 70px));
  }

  .close-button {
    /* Even larger on iPhone 13 Pro Max */
    width: 52px;
    min-width: 52px;
    height: 52px;
    min-height: 52px;
    font-size: 32px;
  }
}

/* Specific fix for iPhone 13 Pro Max exact dimensions */
@media (width: 428px) and (height: 926px) {
  .chart-modal-content {
    /* Specific height for exact iPhone 13 Pro Max dimensions */
    height: calc(100vh - 180px) !important;
    max-height: calc(100vh - 180px) !important;

    /* Balanced margins for this specific device */
    margin-top: 80px !important;
    margin-bottom: 60px !important;
    padding: 18px !important;
  }
}

/* Additional safety for all mobile Safari */
@media (width <= 480px) {
  .chart-modal {
    /* Ensure modal uses safe viewport */
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for mobile */
  }

  .chart-modal-content {
    /* Ultra-safe height calculation */
    height: calc(100vh - 280px);
    max-height: calc(100vh - 280px);

    /* Ensure it never touches edges */
    margin: 120px 8px 100px;
  }
}
