/**
 * Chart Modal Component Styles
 *
 * Styles for the ChartModal component including modal overlay, content,
 * chart container, legend, and close button.
 *
 * Dependencies: variables.css, base.css
 */

/* Chart Modal Styles */
.chart-modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;

  /* iOS Safe Area Support */
  width: 100%;
  width: 100vw;
  height: 100%;
  height: 100vh;
  background: rgb(0 0 0 / 0.95);
  backdrop-filter: var(--glass-blur);
  animation: modal-fade-in 0.3s ease-out;
}

.chart-modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 95vw;
  max-width: 95vw;
  height: 85vh;
  max-height: 85vh;

  /* Ensure modal never exceeds viewport */
  max-height: calc(100vh - 80px);
  padding: 12px;

  /* Add top margin to avoid browser UI overlap */
  margin-top: 40px;
  color: var(--text-primary);
  background: var(--glass-bg-strong);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  box-shadow: var(--glass-shadow);
  backdrop-filter: var(--glass-blur-strong);
  animation: modal-slide-in 0.3s ease-out;
}

.chart-modal-content::before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 1px;
  content: '';
  background: linear-gradient(90deg, transparent, var(--glass-border), transparent);
}

.chart-header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 8px;
  margin-bottom: 8px;
  border-bottom: 1px solid var(--glass-border);
}

.chart-header::after {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  height: 1px;
  content: '';
  background: linear-gradient(90deg, transparent, var(--accent-cyan), transparent);
}

.chart-header h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  text-shadow: 0 2px 10px rgb(0 0 0 / 0.3);
}

.close-button {
  position: relative;

  /* Ensure close button is always accessible */
  z-index: 1002;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;

  /* Better touch target for mobile */
  min-width: 44px;
  height: 44px;
  min-height: 44px;
  padding: 0;
  font-size: 24px;
  color: var(--text-primary);
  cursor: pointer;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 50%;
  backdrop-filter: var(--glass-blur);
  transition: all 0.3s ease;
}

.close-button:hover {
  background: rgb(255 255 255 / 0.2);
  border-color: rgb(255 255 255 / 0.3);
  box-shadow: 0 4px 16px rgb(0 0 0 / 0.2);
  transform: scale(1.1);
}

.close-button:focus {
  outline: 2px solid var(--accent-cyan);
  outline-offset: 2px;
}

.close-button:active {
  transform: scale(0.95);
}

.chart-container {
  position: relative;
  flex: 1;
  width: 100%;
  height: 100%;
  min-width: 600px;
  min-height: 500px;
  margin: 4px 0;
  overflow: hidden;
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  box-shadow: var(--glass-shadow);
}

.chart-container::before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 1px;
  content: '';
  background: linear-gradient(90deg, transparent, var(--accent-teal), transparent);
  opacity: 0.3;
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  justify-content: center;
  padding: 8px;
  margin-top: 4px;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 8px;
  backdrop-filter: var(--glass-blur);
}

.legend-item {
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 13px;
  font-weight: 500;
  color: var(--text-secondary);
}

.legend-color {
  width: 14px;
  height: 14px;
  border-radius: 3px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 0.3);
}

/* Mobile-specific fixes for browser UI overlap */
@media (width <= 768px) {
  .chart-modal-content {
    /* Maximize chart size while keeping bottom visible */
    height: calc(100vh - 140px);
    max-height: calc(100vh - 140px);

    /* Minimal padding to maximize chart space */
    padding: 8px;
    padding-top: max(8px, env(safe-area-inset-top, 8px));
    padding-bottom: max(8px, env(safe-area-inset-bottom, 8px));

    /* Smaller margins to maximize space */
    margin-top: 60px;
    margin-top: max(60px, env(safe-area-inset-top, 60px));

    margin-bottom: 40px;
    margin-bottom: max(40px, env(safe-area-inset-bottom, 40px));
  }

  .close-button {
    /* Ensure it's always visible */
    z-index: 1003;

    /* Larger close button on mobile */
    width: 48px;
    min-width: 48px;
    height: 48px;
    min-height: 48px;
    font-size: 28px;
  }

  .chart-container {
    /* Maximize chart on mobile */
    min-width: 300px;
    min-height: 450px;
    margin: 2px 0;
  }

  .chart-header {
    /* Compact header on mobile */
    padding-bottom: 4px;
    margin-bottom: 4px;
  }

  .chart-legend {
    /* Compact legend on mobile */
    padding: 4px;
    margin-top: 2px;
    gap: 8px;
  }
}

/* iPhone 13 Pro Max and similar large phones */
@media (width <= 428px) and (height <= 926px) {
  .chart-modal-content {
    /* Maximize chart for iPhone 13 Pro Max */
    height: calc(100vh - 160px);
    max-height: calc(100vh - 160px);

    /* Minimal padding to maximize chart */
    padding: 6px;

    /* Smaller margins to maximize space */
    margin-top: 70px;
    margin-top: max(70px, env(safe-area-inset-top, 70px));
    margin-bottom: 50px;
    margin-bottom: max(50px, env(safe-area-inset-bottom, 50px));
  }

  .close-button {
    /* Even larger on iPhone 13 Pro Max */
    width: 52px;
    min-width: 52px;
    height: 52px;
    min-height: 52px;
    font-size: 32px;
  }
}

/* Specific fix for iPhone 13 Pro Max exact dimensions */
@media (width: 428px) and (height: 926px) {
  .chart-modal-content {
    /* Maximum chart size for exact iPhone 13 Pro Max dimensions */
    height: calc(100vh - 140px) !important;
    max-height: calc(100vh - 140px) !important;

    /* Minimal margins and padding for maximum chart space */
    margin-top: 60px !important;
    margin-bottom: 40px !important;
    padding: 4px !important;
  }

  .chart-container {
    /* Maximum chart size on iPhone 13 Pro Max */
    min-height: 500px !important;
    margin: 1px 0 !important;
  }
}

/* Additional safety for all mobile Safari */
@media (width <= 480px) {
  .chart-modal {
    /* Ensure modal uses safe viewport */
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for mobile */
  }

  .chart-modal-content {
    /* Ultra-safe height calculation */
    height: calc(100vh - 280px);
    max-height: calc(100vh - 280px);

    /* Ensure it never touches edges */
    margin: 120px 8px 100px;
  }
}
