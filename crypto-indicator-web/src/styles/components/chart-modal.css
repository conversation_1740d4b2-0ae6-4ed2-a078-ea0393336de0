/**
 * Chart Modal Component Styles
 *
 * Styles for the ChartModal component including modal overlay, content,
 * chart container, legend, and close button.
 *
 * Dependencies: variables.css, base.css
 */

/* Chart Modal Styles */
.chart-modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  /* iOS Safe Area Support */
  width: 100vw;
  height: 100vh;
  background: rgb(0 0 0 / 0.95);
  backdrop-filter: var(--glass-blur);
  animation: modal-fade-in 0.3s ease-out;
}

.chart-modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 95vw;
  max-width: 95vw;
  height: 75vh;
  max-height: 75vh;
  padding: 24px;
  /* Add top margin to avoid browser UI overlap */
  margin-top: 60px;
  color: var(--text-primary);
  background: var(--glass-bg-strong);
  border: 1px solid var(--glass-border);
  border-radius: 24px;
  box-shadow: var(--glass-shadow);
  backdrop-filter: var(--glass-blur-strong);
  animation: modal-slide-in 0.3s ease-out;
}

.chart-modal-content::before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 1px;
  content: '';
  background: linear-gradient(90deg, transparent, var(--glass-border), transparent);
}

.chart-header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 15px;
  margin-bottom: 20px;
  border-bottom: 1px solid var(--glass-border);
}

.chart-header::after {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  height: 1px;
  content: '';
  background: linear-gradient(90deg, transparent, var(--accent-cyan), transparent);
}

.chart-header h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  text-shadow: 0 2px 10px rgb(0 0 0 / 0.3);
}

.close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  padding: 0;
  font-size: 24px;
  color: var(--text-primary);
  cursor: pointer;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 50%;
  backdrop-filter: var(--glass-blur);
  transition: all 0.3s ease;
  /* Ensure close button is always accessible */
  z-index: 1002;
  position: relative;
  /* Better touch target for mobile */
  min-width: 44px;
  min-height: 44px;
}

.close-button:hover {
  background: rgb(255 255 255 / 0.2);
  border-color: rgb(255 255 255 / 0.3);
  box-shadow: 0 4px 16px rgb(0 0 0 / 0.2);
  transform: scale(1.1);
}

.close-button:focus {
  outline: 2px solid var(--accent-cyan);
  outline-offset: 2px;
}

.close-button:active {
  transform: scale(0.95);
}

.chart-container {
  position: relative;
  flex: 1;
  width: 100%;
  min-width: 600px;
  min-height: 400px;
  margin: 20px 0;
  overflow: hidden;
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 16px;
  box-shadow: var(--glass-shadow);
}

.chart-container::before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 1px;
  content: '';
  background: linear-gradient(90deg, transparent, var(--accent-teal), transparent);
  opacity: 0.3;
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  justify-content: center;
  padding: 16px;
  margin-top: 20px;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  backdrop-filter: var(--glass-blur);
}

.legend-item {
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 13px;
  font-weight: 500;
  color: var(--text-secondary);
}

.legend-color {
  width: 14px;
  height: 14px;
  border-radius: 3px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 0.3);
}
