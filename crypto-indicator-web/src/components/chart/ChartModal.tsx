import React, { useEffect } from "react";

import { useMobileSafariDetection } from "@/hooks/useMobileSafariDetection";

import { ChartContainer } from "./ChartContainer";
import { ChartHeader } from "./ChartHeader";

import type { ChartModalProps } from "@/types/chart";

// Component-specific styles
import '../../styles/components/chart-modal.css';

// Constants for mobile Safari viewport calculations
const MOBILE_SAFARI_MARGIN_OFFSET = 180;
const MOBILE_SAFARI_HEIGHT_RATIO = 0.75;

export const ChartModal: React.FC<ChartModalProps> = ({
  data,
  onClose,
}) => {
  const { isMobileSafari, viewportHeight } = useMobileSafariDetection();

  const handleBackdropClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        onClose();
      }
    };

    document.addEventListener("keydown", handleEscape);
    return () => { document.removeEventListener("keydown", handleEscape); };
  }, [onClose]);

  return (
    // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-noninteractive-element-interactions
    <div
      className="chart-modal"
      onClick={handleBackdropClick}
      role="dialog"
      aria-modal="true"
      aria-label="Chart modal"
    >
      <div
        className={`chart-modal-content ${isMobileSafari ? 'mobile-safari-fix' : ''}`}
        style={isMobileSafari ? {
          height: `${Math.min(viewportHeight - MOBILE_SAFARI_MARGIN_OFFSET, viewportHeight * MOBILE_SAFARI_HEIGHT_RATIO)}px`,
          maxHeight: `${Math.min(viewportHeight - MOBILE_SAFARI_MARGIN_OFFSET, viewportHeight * MOBILE_SAFARI_HEIGHT_RATIO)}px`,
          marginTop: '80px',
          marginBottom: '60px',
        } : undefined}
      >
        <ChartHeader
          symbol={data.symbol}
          conversionCurrency={data.conversionCurrency}
          onClose={onClose}
        />
        <ChartContainer data={data} />
      </div>
    </div>
  );
};
