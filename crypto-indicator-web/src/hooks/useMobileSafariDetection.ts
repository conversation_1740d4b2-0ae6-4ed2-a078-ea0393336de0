import { useEffect, useState } from 'react';

// iPhone 13 Pro Max width threshold
const IPHONE_13_PRO_MAX_WIDTH = 428;

interface MobileSafariState {
  isMobileSafari: boolean;
  viewportHeight: number;
}

export const useMobileSafariDetection = (): MobileSafariState => {
  const [state, setState] = useState<MobileSafariState>({
    isMobileSafari: false,
    viewportHeight: 0,
  });

  useEffect(() => {
    const detectMobileSafari = () => {
      const { userAgent } = navigator;
      const isMobile = /iphone|ipad|ipod|android/i.test(userAgent);
      const isSafari = /safari/i.test(userAgent) && !/chrome|crios|fxios/i.test(userAgent);
      return isMobile && (isSafari || window.innerWidth <= IPHONE_13_PRO_MAX_WIDTH);
    };

    const handleViewportChange = () => {
      const isMobileSafari = detectMobileSafari();
      const viewportHeight = window.innerHeight;
      
      setState({ isMobileSafari, viewportHeight });
      
      // Force layout recalculation on mobile
      if (isMobileSafari) {
        // Small delay to ensure browser UI changes are complete
        setTimeout(() => {
          window.dispatchEvent(new Event('resize'));
        }, 100);
      }
    };

    // Initial detection
    handleViewportChange();

    // Listen for viewport changes (orientation, browser UI show/hide)
    window.addEventListener('resize', handleViewportChange);
    window.addEventListener('orientationchange', handleViewportChange);
    
    // Listen for visual viewport changes (iOS Safari address bar)
    if ('visualViewport' in window && window.visualViewport) {
      window.visualViewport.addEventListener('resize', handleViewportChange);
    }

    return () => {
      window.removeEventListener('resize', handleViewportChange);
      window.removeEventListener('orientationchange', handleViewportChange);
      if ('visualViewport' in window && window.visualViewport) {
        window.visualViewport.removeEventListener('resize', handleViewportChange);
      }
    };
  }, []);

  return state;
};
