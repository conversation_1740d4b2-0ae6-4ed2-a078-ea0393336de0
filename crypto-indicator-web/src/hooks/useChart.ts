import { useEffect, useRef } from "react";

import { CHART_CONSTANTS } from "@/constants/chart";
import { chartHelpers } from "@/utils/chartHelpers";

import type { ChartDataSource } from "@/types/chart";
import type { I<PERSON>hart<PERSON><PERSON> } from "lightweight-charts";

interface UseChartReturn {
  chartRef: React.RefObject<IChartApi | null>;
  containerRef: React.RefObject<HTMLDivElement | null>;
}

/**
 * Hook for managing chart lifecycle and interactions
 * Handles chart creation, data setup, and cleanup
 */
export const useChart = (data: ChartDataSource): UseChartReturn => {
  const chartRef = useRef<IChartApi | null>(null);
  const containerRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (containerRef.current === null) {return;}

    // Capture the current container reference for cleanup
    const currentContainer = containerRef.current;

    // Small delay to ensure container dimensions are calculated
    const timeoutId = setTimeout(() => {
      if (containerRef.current === null) {return;}

      // Create chart instance
      const chart = chartHelpers.createChart(containerRef.current);
      chartRef.current = chart;

      // Setup series with data
      // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
      chartHelpers.setupChartSeries(chart, data);
    }, CHART_CONSTANTS.CHART_INIT_DELAY_MS);



    // Handle fast zoom with mouse wheel
    const handleWheel = (event: WheelEvent) => {
      if (chartRef.current) {
        event.preventDefault();
        const zoomIn = event.deltaY < 0;
        chartHelpers.fastZoom(chartRef.current, zoomIn);
      }
    };



    if (currentContainer !== null) {
      currentContainer.addEventListener("wheel", handleWheel, { passive: false });
    }

    // Cleanup function
    return () => {
      clearTimeout(timeoutId);

      if (currentContainer !== null) {
        currentContainer.removeEventListener("wheel", handleWheel);
      }
      if (chartRef.current) {
        chartHelpers.cleanup(chartRef.current);
        chartRef.current = null;
      }
    };
  }, [data]);

  return {
    chartRef,
    containerRef,
  };
};
